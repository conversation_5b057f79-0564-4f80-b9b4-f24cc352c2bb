﻿  capture.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“capture/capture.cpp”)
  
  capture_utils.cpp
  duplication_api_capture.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“capture/duplication_api_capture.cpp”)
  
  virtual_camera.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“capture/virtual_camera.cpp”)
  
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\capture\virtual_camera.cpp(122,47): warning C4566: 由通用字符名称“\u202F”表示的字符不能在当前代码页(936)中表示出来
  winrt_capture.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“capture/winrt_capture.cpp”)
  
  config.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“config/config.cpp”)
  
  detection_buffer.cpp
  dml_detector.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\detector\dml_detector.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“detector/dml_detector.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: “=”: 从“wchar_t”转换到“char”，可能丢失数据
  (编译源文件“detector/dml_detector.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\detector\dml_detector.cpp(41,23):
          查看对正在编译的函数 模板 实例化“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)”的引用
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\detector\dml_detector.cpp(41,5):
              请参阅 "GetDMLDeviceName" 中对 "std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string" 的第一个引用
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          查看对正在编译的函数 模板 实例化“void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)”的引用
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          查看对正在编译的函数 模板 实例化“_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)”的引用
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  postProcess.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“detector/postProcess.cpp”)
  
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\detector\postProcess.cpp(240,30): warning C4244: “参数”: 从“int64_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\detector\postProcess.cpp(240,24): warning C4244: “参数”: 从“int64_t”转换到“int”，可能丢失数据
  imgui.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  keyboard_listener.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“keyboard/keyboard_listener.cpp”)
  
  keycodes.cpp
  ghub.cpp
  kmboxNetConnection.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“mouse/kmboxNetConnection.cpp”)
  
  Kmbox_b.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“mouse/Kmbox_b.cpp”)
  
  正在编译...
  kmboxNet.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\mouse\kmbox_net\HidTable.h(119,9): warning C4005: “KEY_EXECUTE”: 宏重定义
  (编译源文件“mouse/kmbox_net/kmboxNet.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      参见“KEY_EXECUTE”的前一个定义
  
  mouse.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“mouse/mouse.cpp”)
  
  SerialConnection.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“mouse/SerialConnection.cpp”)
  
  AimbotTarget.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“mouse/AimbotTarget.cpp”)
  
  draw_ai.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_ai.cpp”)
  
  draw_buttons.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_buttons.cpp”)
  
  draw_capture.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\overlay\draw_capture.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_capture.cpp”)
  
  draw_debug.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_debug.cpp”)
  
  draw_mouse.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_mouse.cpp”)
  
  draw_overlay.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_overlay.cpp”)
  
  draw_stats.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_stats.cpp”)
  
  draw_target.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/draw_target.cpp”)
  
  overlay.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\overlay\overlay.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“overlay/overlay.cpp”)
  
  other_tools.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“scr/other_tools.cpp”)
  
  sunone_aimbot_cpp.cpp
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\config\config.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“sunone_aimbot_cpp.cpp”)
  
  正在生成代码
C:\Users\<USER>\Desktop\aimbot\备份-完成编写\sunone_aimbot_cpp\scr\other_tools.cpp(301): warning C4715: “get_ghub_version”: 不是所有的控件路径都返回值
  5965 of 6069 functions (98.3%) were compiled, the rest were copied from previous compilation.
    98 functions were new in current compilation
    36 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  sunone_aimbot_cpp.vcxproj -> C:\Users\<USER>\Desktop\aimbot\备份-完成编写\x64\DML\QQMusic.exe
